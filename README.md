<p align="center"><a href="https://www.xu.edu.ph/" target="_blank"><img src="https://www.xu.edu.ph/images/system_images/logo.png" width="400" alt="XU Logo"></a></p>

# SLP Online Database Web App

Welcome to the SLP (Service Learning Program) Online Database Web App for Xavier University - Ateneo de Cagayan! This web application is designed to streamline the management and access of data related to the Service Learning Program offered by Xavier University. It utilizes Google Sheets as the database backend for storing and managing data and integrates Google authentication for user login.

## Features

-   **Google Authentication**: Secure login system using Google accounts for the SLP Faculty and Registrar.
-   **SLP Project Management**: Manage project details, including descriptions and student tracking.
-   **Data Management with Google Sheets**: Utilizes Google Sheets API for adding, storing, and managing data securely in the cloud.
-   **Reporting and Analytics**: Generate reports and analyze data to assess the impact and effectiveness of service learning projects.

## Installation

1. Clone the repository:

    ```bash
    git clone https://github.com/IsaacMartelAbogatal/slpweb-app.git
    ```

2. Navigate into the project directory:

    ```bash
    cd slpweb-app
    ```

3. Install Laravel Sail:

    ```bash
    composer require laravel/sail --dev
    ```

4. Create a copy of the `.env.example` file and rename it to `.env`:

    ```bash
    cp .env.example .env
    ```

5. Install Sail:

    ```bash
    php artisan sail:install
    ```

6. Start the application using Sail:

    ```bash
    ./vendor/bin/sail up -d
    ```

7. Run database migrations:

    ```bash
    ./vendor/bin/sail artisan migrate
    ```

8. Generate the application key:

    ```bash
    ./vendor/bin/sail artisan key:generate
    ```

9. Visit `http://localhost` in your web browser to access the application.

## Usage

-   **Google Authentication**: Only authorized users can log in using their Google accounts linked to Xavier University.
-   **SLP Page**: Authorized SLP Faculty members can add, edit, and delete entries from the web app. The changes will then reflect on the SLP Database Google Sheet. Full access of the web app is provided.
-   **Registrar Page**: The Registrar can view the students list using the DataTable and generate reports. Only limited access of the web app is provided.

## Collaborators

-   Isaac Martel V. Abogatal - Full-stack developer
-   Allian Mar D. Pelisco - Program Tester
-   Steven Jeremy G. Tan - Backend developer
