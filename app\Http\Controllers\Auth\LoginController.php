<?php
namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Laravel\Socialite\Facades\Socialite;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;

class LoginController extends Controller
{
    public function showLoginForm()
    {
        // Here you can return the view for your login form
        return view('auth.login');
    }

    public function redirectToGoogle()
    {
        return Socialite::driver('google')->redirect();
    }

    public function handleGoogleCallback(Request $request)
    {
        try {
            $googleUser = Socialite::driver('google')->user();

            // Check if the user exists in the database
            $user = User::where('email', $googleUser->getEmail())->first();

            // If the user does not exist, create a new user
            if (!$user) {
                // Default role is assigned as '0'
                $role = '0'; 

                $user = User::create([
                    'name' => $googleUser->getName(),
                    'email' => $googleUser->getEmail(),
                    // Generate a random password
                    'password' => bcrypt(Str::random(16)),
                    'role_as' => $role, // Assign role
                ]);
            }
            
            // Log in the user
            Auth::login($user);

            // Retrieve the access token from the Socialite response
            $accessToken = $googleUser->token;

            // Store the access token securely (e.g., in session)
            Session::put('google_access_token', $accessToken);

            // Redirect the user to the desired route based on role_as
            if ($user->role_as == '1') {
                return redirect('/slp-sheets');
            } else {
                return redirect('/registrar-sheets');
            }
        } catch (\Laravel\Socialite\Two\InvalidStateException $e) {
            // Handle the exception if needed
            return redirect()->route('login')->with('error', 'Something went wrong. Please try logging in again.');
        }
    }
    
    public function logout()
    {
        Auth::logout();
        return redirect('/');
        
    }
}
