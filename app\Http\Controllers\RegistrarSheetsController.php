<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Google_Client;
use Google_Service_Sheets;

class RegistrarSheetsController extends Controller
{
    public function index()
    {
        $client = new Google_Client();
        $client->setApplicationName('Google Sheets App');
        $client->setScopes([Google_Service_Sheets::SPREADSHEETS_READONLY]);
        $client->setAuthConfig(base_path('credentials.json'));

        $service = new Google_Service_Sheets($client);

        $spreadsheetId = '1mzV46-cw3JHs65AcR75YtAcT32t6obfg2bfsaohuefk';
        $range = 'Sheet1!A2:N';
        $response = $service->spreadsheets_values->get($spreadsheetId, $range);
        $values = $response->getValues();

        return view('registrar.index', ['values' => $values]);
    }
}
