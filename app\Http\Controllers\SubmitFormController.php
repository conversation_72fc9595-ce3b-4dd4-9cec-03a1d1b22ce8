<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Google\Client as GoogleClient;
use Session;
use Exception;

class SubmitFormController extends Controller
{
    public function submitForm(Request $request)
    {
        try {
            // Validate request data if needed

            // Get form data
            $formData = $request->all();

            // Prepare data to be inserted into Google Sheets
            $rowData = [
                $formData['slmisId'],
                $formData['lastName'],
                $formData['firstName'],
                $formData['middleInitial'],
                $formData['idNumber'],
                $formData['academicProgram'],
                $formData['slProjectTitle'],
                $formData['siteOfProject'],
                $formData['partnerInstitution'],
                $formData['semester'],
                $formData['schoolYear'],
                $formData['subjectCode'],
                $formData['faculty'],
                $formData['projectCode'],
                $formData['verified'],
                // Add other form fields here
            ];


            // Initialize Google Sheets API client
            $client = new GoogleClient();
            $client->setApplicationName('Google Sheets App');
            $client->setScopes([\Google\Service\Sheets::SPREADSHEETS]);
            $client->setAuthConfig(base_path('credentials.json'));

            // Create Google Sheets service
            $service = new \Google\Service\Sheets($client);

            // Spreadsheet ID and range
            $spreadsheetId = '1mzV46-cw3JHs65AcR75YtAcT32t6obfg2bfsaohuefk';
            $range = 'Sheet1'; // Assuming data is inserted into Sheet1

            // Insert data into Google Sheets
            $requestBody = new \Google\Service\Sheets\ValueRange([
                'values' => [$rowData]
            ]);
            $params = [
                'valueInputOption' => 'RAW'
            ];
            $result = $service->spreadsheets_values->append($spreadsheetId, $range, $requestBody, $params);

            // Check if data is successfully inserted
            if ($result->getUpdates()->getUpdatedCells() > 0) {
                return response()->json(['success' => true]);
            } else {
                return response()->json(['success' => false, 'message' => 'Failed to insert data into Google Sheets']);
            }
        } catch (\Exception $e) {
            // Log the error
            \Log::error('Error submitting form: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred while processing your request.']);
        }
    }
    public function fetchEntry($slmisId)
    {
        \Log::info('Fetching entry with ID: ' . $slmisId);
        try {
            \Log::info('Initializing Google Sheets API client');
            $client = new GoogleClient();
            $client->setApplicationName('Google Sheets App');
            $client->setScopes([\Google\Service\Sheets::SPREADSHEETS]);
            $client->setAuthConfig(base_path('credentials.json'));

            $service = new \Google\Service\Sheets($client);
            $spreadsheetId = '1mzV46-cw3JHs65AcR75YtAcT32t6obfg2bfsaohuefk';
            $range = 'Sheet1';
            \Log::info('Fetching data from Google Sheets');
            $response = $service->spreadsheets_values->get($spreadsheetId, $range);
            $values = $response->getValues();

            \Log::info('Google Sheets API response: ', $values); // Log the response

            foreach ($values as $row) {
                \Log::info('Processing row: ', $row); // Log each row
                if ($row[0] == $slmisId) {
                    return response()->json([
                        'success' => true,
                        'data' => [
                            'slmisId' => $row[0],
                            'lastName' => $row[1],
                            'firstName' => $row[2],
                            'middleInitial' => $row[3],
                            'idNumber' => $row[4],
                            'academicProgram' => $row[5],
                            'slProjectTitle' => $row[6],
                            'siteOfProject' => $row[7],
                            'partnerInstitution' => $row[8],
                            'semester' => $row[9],
                            'schoolYear' => $row[10],
                            'subjectCode' => $row[11],
                            'faculty' => $row[12],
                            'projectCode' => $row[13],
                            'verified' => $row[14],
                        ]
                    ]);
                }
            }
            \Log::warning('Entry not found for ID: ' . $slmisId);
            return response()->json([
                'success' => false,
                'message' => 'Entry not found'
            ], 404);
        } catch (\Google\Service\Exception $e) {
            \Log::error('Google Service Exception: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Google Service Exception: ' . $e->getMessage()
            ], 500);
        } catch (\Exception $e) {
            \Log::error('General Exception: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'General Exception: ' . $e->getMessage()
            ], 500);
        }
    }
    public function updateEntry(Request $request, $slmisId)
    {
        try {
            // Validate request data if needed

            // Get form data
            $formData = $request->all();

            // Prepare data to be updated in Google Sheets
            $rowData = [
                $formData['slmisId'],
                $formData['lastName'],
                $formData['firstName'],
                $formData['middleInitial'],
                $formData['idNumber'],
                $formData['academicProgram'],
                $formData['slProjectTitle'],
                $formData['siteOfProject'],
                $formData['partnerInstitution'],
                $formData['semester'],
                $formData['schoolYear'],
                $formData['subjectCode'],
                $formData['faculty'],
                $formData['projectCode'],
                $formData['verified'],
            ];

            // Initialize Google Sheets API client
            $client = new GoogleClient();
            $client->setApplicationName('Google Sheets App');
            $client->setScopes([\Google\Service\Sheets::SPREADSHEETS]);
            $client->setAuthConfig(base_path('credentials.json'));

            // Create Google Sheets service
            $service = new \Google\Service\Sheets($client);

            // Spreadsheet ID and range
            $spreadsheetId = '1mzV46-cw3JHs65AcR75YtAcT32t6obfg2bfsaohuefk';
            $range = 'Sheet1';

            // Find the row index of the entry to be updated
            $response = $service->spreadsheets_values->get($spreadsheetId, $range);
            $values = $response->getValues();
            $rowIndex = null;
            foreach ($values as $index => $row) {
                if ($row[0] == $slmisId) {
                    $rowIndex = $index + 1; // Adjust for 0-based indexing
                    break;
                }
            }

            // Update data in Google Sheets
            if ($rowIndex !== null) {
                $requestBody = new \Google\Service\Sheets\ValueRange([
                    'values' => [$rowData]
                ]);
                $params = [
                    'valueInputOption' => 'RAW'
                ];
                $result = $service->spreadsheets_values->update($spreadsheetId, "Sheet1!A$rowIndex", $requestBody, $params);

                // Check if data is successfully updated
                if ($result->getUpdatedCells() > 0) {
                    return response()->json(['success' => true, 'message' => 'Entry updated successfully!']);
                } else {
                    return response()->json(['success' => false, 'message' => 'Failed to update entry. Please try again.']);
                }
            } else {
                return response()->json(['success' => false, 'message' => 'Entry not found.']);
            }
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }

}