<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CheckRole
{
    public function handle(Request $request, Closure $next)
    {
        // Redirect logged-in users attempting to access the login page
        if (Auth::check() && $request->route()->getName() == 'login') {
            // Redirect users based on their roles
            $user = Auth::user();
            if ($user->role_as == 0) {
                return redirect()->route('registrar-sheets')->with('error', 'You are already logged in.');
            } else {
                // Redirect users with other roles as needed
                return redirect()->route('dashboard')->with('error', 'You are already logged in.');
            }
        }

        // Check if user is logged in
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', 'You must be logged in to access this page.');
        }

        // Retrieve user information
        $user = Auth::user();

        // Perform role-based access control
        if ($user->role_as == 0 && $request->route()->getName() == 'slp-sheets') {
            return redirect()->route('registrar-sheets')->with('error', 'You do not have permission to access this page.');
        }

        // Proceed to the next middleware
        return $next($request);
    }
}
