body {
    margin: 0;
    padding: 0;
    font-family: Georgia, serif;
    background-image: url('/images/xulogin.jpg'); 
    background-size: cover;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.login-box {
    border: 2px solid white; /* Border color */
    border-radius: 10px;
    overflow: hidden; /* Prevents border from leaking outside */
    width: 450px;
}

.login-container {
    background-color: white; /* Adjust opacity as needed */
    padding: 10px;
    text-align: center;
}

h1 {
    margin-bottom: 20px;
}

.logo {
    width: 400px; /* Adjust the width of the image */
}

a.google-login-button {
    display: inline-block;
    background-color: #283971; 
    text-decoration: none;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: bold;
    transition: background-color 0.3s ease;
    color: white;
}

a.google-login-button:hover {
    background-color: rgb(8, 0, 79);
}
