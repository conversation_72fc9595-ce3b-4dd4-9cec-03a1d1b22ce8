/* Reset default browser styles */
html, body {
    margin: 0;
    padding: 0;
}

table {
    border-collapse: collapse;
    width: 100%;
    border: 1px solid #ddd;
}

th, td {
    border: 1px solid #ddd;
    padding: 12px; /* Increased padding */
    text-align: left;
    font-size: 20px; /* Increased font size */
}

th {
    background-color: #f2f2f2;
}

tr:nth-child(even) {
    background-color: #f2f2f2;
}

.action-btn {
    padding: 8px 16px; /* Increased padding for button */
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}
.red-btn {
    background-color: red;
}

/* Navbar container */
.navbar {
    overflow: hidden;
    background-color: #283971; /* Dark background color */
    padding: 10px 20px; /* Add some padding */
    margin-bottom: 20px; /* Add margin to separate navbar from table */
    border-radius: 0; /* Remove border-radius */
}

/* Navbar links */
.navbar a { 
    float: right;
    display: block;
    color: white;
    text-align: center;
    padding: 14px 20px;
    text-decoration: none;
}

/* Navbar links on hover */
.navbar a:hover {
    background-color: #ddd; /* Light background color on hover */
    color: black;
}

/* Placeholder image */
.profile-pic {
    float: left;
    margin-right: 20px;
    width: 200px;
    height: 55px;
}

/* Active navbar link */
.navbar a.active {
    background-color: #D9A20D; 
    color: black;
}
