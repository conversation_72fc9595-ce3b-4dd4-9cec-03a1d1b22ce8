/* Reset default browser styles */
html, body {
    margin: 0;
    padding: 0;
}

table {
    border-collapse: collapse;
    width: 100%;
    border: 1px solid #ddd;
}

th, td {
    border: 1px solid #ddd;
    padding: 12px; /* Increased padding */
    text-align: left;
    font-size: 20px; /* Increased font size */
}

th {
    background-color: #f2f2f2;
}

tr:nth-child(even) {
    background-color: #f2f2f2;
}

.action-btn {
    padding: 8px 16px; /* Increased padding for button */
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-bottom: 10px; /* Added bottom margin */
}

.success-btn {
    padding: 12px 24px;
    font-size: 20px;
    font-weight:bold;
    background-color: #28a745; /* Green color for success */
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-left: 10px; /* Added left margin */
    margin-bottom: 10px;
    margin-top: 10px;
}

.secondary-btn {
    background-color: #ffffff;
    color: #333333;
    border: 1px solid #333333;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s, color 0.3s;
    margin-left: 10px;
}

/* Navbar container */
.navbar {
    overflow: hidden;
    background-color: #283971; /* Dark background color */
    padding: 10px 20px; /* Add some padding */
    border-radius: 0; /* Remove border-radius */
    margin-bottom: 20px; /* Add margin to separate navbar from table */
}

/* Navbar links */
.navbar a {
    float: right;
    display: block;
    color: white;
    text-align: center;
    padding: 14px 20px;
    text-decoration: none;
}

/* Navbar links on hover */
.navbar a:hover {
    background-color: #ddd; /* Light background color on hover */
    color: black;
}
/* Placeholder image */
.profile-pic {
    float: left;
    margin-right: 20px;
    width: 200px;
    height: 55px;
}

/* Active navbar link */
.navbar a.active {
    background-color: #D9A20D; 
    color: black;
}

.filters-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.filter-input {
    margin: 0 5px; /* Adjust margin as needed */
}

.red-btn {
    padding: 12px 24px;
    font-size: 20px;
    font-weight:bold;
    background-color: red; /* Green color for success */
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-left: 10px; /* Added left margin */
    margin-bottom: 10px;
    margin-top: 10px;
}

.container {
    max-width: 600px; /* Adjust width as needed */
    margin: 0 auto; /* Center the container */
}

/* Form group styling */
.form-group {
    margin-bottom: 20px; /* Add space between form groups */
}

/* Label styling */
label {
    display: block;
    font-weight: bold;
}

/* Input field styling */
input[type="text"] {
    width: 100%;
    padding: 10px;
    margin-top: 5px;
    margin-bottom: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box; /* Ensure padding and border are included in the width */
}

/* Button styling */
.success-btn {
    padding: 12px 24px;
    font-size: 20px;
    font-weight: bold;
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 10px;
}
.success-btn:hover {
    background-color: darkgreen;
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-left: 10px;
}

.red-btn {
    background-color: red;
    color: white;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    font-size: 16px;
    border-radius: 5px;
    transition: background-color 0.3s;
    margin-right: 10px; /* Add margin to separate it from the text */
}

.red-btn:hover {
    background-color: darkred;
}