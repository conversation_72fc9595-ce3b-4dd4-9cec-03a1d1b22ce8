<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registrar</title>
    <!-- Include DataTables CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.css">

    <!-- Include jQuery -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

    <!-- Include DataTables JS -->
    <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.js"></script>

    <!-- Link to your separated CSS file -->
    <link rel="stylesheet" type="text/css" href="{{ asset('resources/css/registrar.css') }}">
</head>

<body>

    <!-- Navbar -->
    <div class="navbar">

        <img src="https://www.xu.edu.ph/images/system_images/logo.png" alt="Profile Picture" class="profile-pic">

        <!-- Navbar links -->
        <a href="{{ route('logout') }}">Logout</a>
        @if (Auth::check() && Auth::user()->role_as != 0)
            <a href="/registrar-sheets">Registrar</a>
            <a href="/slp-sheets">SLP</a>
        @endif
    </div>

    <table id="dataTable">
        <thead>
            <tr>
                <th>SLMIS ID</th>
                <th>Last Name</th>
                <th>First Name</th>
                <th>Middle Initial</th>
                <th>ID Number</th>
                <th>Academic Program</th>
                <th>SL Project Title</th>
                <th>Site of Project</th>
                <th>Partner Institution</th>
                <th>Semester</th>
                <th>School Year</th>
                <th>Subject Code</th>
                <th>Faculty</th>
                <th>Project Code</th>
                <th>More Details</th> <!-- New column for action button -->
            </tr>
        </thead>
        <tbody>
            @foreach ($values as $row)
                <tr>
                    <td>{{ $row[0] }}</td>
                    <td>{{ $row[1] }}</td>
                    <td>{{ $row[2] }}</td>
                    <td>{{ $row[3] }}</td>
                    <td>{{ $row[4] }}</td>
                    <td>{{ $row[5] }}</td>
                    <td>{{ $row[6] }}</td>
                    <td>{{ $row[7] }}</td>
                    <td>{{ $row[8] }}</td>
                    <td>{{ $row[9] }}</td>
                    <td>{{ $row[10] }}</td>
                    <td>{{ $row[11] }}</td>
                    <td>{{ $row[12] }}</td>
                    <td>{{ $row[13] }}</td>
                    <td><button class="action-btn more-btn">More</button></td> <!-- Button in each row -->
                </tr>
            @endforeach
        </tbody>
    </table>

    <script>
        $(document).ready(function() {
            var table = $('#dataTable').DataTable({
                "columnDefs": [{
                    "targets": [6, 7, 8, 9, 10, 11, 12, 13], // Hide columns 6 to 13
                    "visible": false,
                    "searchable": false
                }]
            });

            // Event delegation for "More" button click
            $('#dataTable').on('click', '.more-btn', function() {
                var $tr = $(this).closest('tr');
                var row = table.row($tr);
                var rowData = row.data();
                var $button = $(this);

                if (row.child.isShown()) {
                    // This row is already open - close it
                    row.child.hide();
                    $tr.removeClass('shown');
                    $button.text('More');
                    $button.removeClass('red-btn'); // Remove the red button class
                } else {
                    // Open this row
                    row.child(format(rowData)).show();
                    $tr.addClass('shown');
                    $button.text('Hide');
                    $button.addClass('red-btn'); // Add the red button class
                }
            });

            function format(rowData) {
                var html = '<div style="padding: 20px;"><h3>Additional Information:</h3>';
                html += '<p>Academic Program: ' + rowData[5] + '</p>'; // Academic Program
                html += '<p>SL Project Title: ' + rowData[6] + '</p>'; // SL Project Title
                html += '<p>Site of Project: ' + rowData[7] + '</p>'; // Site of Project
                html += '<p>Partner Institution: ' + rowData[8] + '</p>'; // Partner Institution
                html += '<p>Semester: ' + rowData[9] + '</p>'; // Semester
                html += '<p>School Year: ' + rowData[10] + '</p>'; // School Year
                html += '<p>Subject Code: ' + rowData[11] + '</p>'; // Subject Code
                html += '<p>Faculty: ' + rowData[12] + '</p>'; // Faculty
                html += '<p>Project Code: ' + rowData[13] + '</p><br>'; // Project Code

                // Additional text with a copy button
                var additionalText = 'Completed Service Learning Project entitled ' + rowData[6] + ' last ' +
                    rowData[9] + ", A.Y. " + rowData[10] + '. ';
                html += '<p id="additionalText">' + additionalText +
                    '<button class="copy-text-btn">Copy Text</button></p>';

                html += '</div>';
                return html;
            }

            // Copy text function
            $(document).on('click', '.copy-text-btn', function() {
                var text = $('#additionalText').text();
                // Remove the word "Copy" from the text
                text = text.replace('Copy Text', '');
                copyTextToClipboard(text);
                //alert("Text copied to clipboard: " + text);
            });

            function copyTextToClipboard(text) {
                var $tempInput = $("<textarea>");
                $("body").append($tempInput);
                $tempInput.val(text).select();
                document.execCommand("copy");
                $tempInput.remove();
            }

            // Highlight Registrar link in navbar if user is on /registrar-sheets
            if (window.location.href.includes("/registrar-sheets")) {
                $(".navbar a[href='/registrar-sheets']").addClass("active");
            }
        });
    </script>

</body>

</html>
