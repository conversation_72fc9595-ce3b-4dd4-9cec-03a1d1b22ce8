<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SLP</title>
    <!-- Include DataTables CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.css">

    <!-- Include jQuery -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

    <!-- Include DataTables JS -->
    <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.js"></script>

    <!-- Link to your separated CSS file -->
    <link rel="stylesheet" type="text/css" href="{{ asset('resources/css/slp.css') }}">

    <meta name="csrf-token" content="{{ csrf_token() }}">
</head>

<body>

    <!-- Navbar -->
    <div class="navbar">
        <img src="https://www.xu.edu.ph/images/system_images/logo.png" alt="Profile Picture" class="profile-pic">
        <!-- Navbar links -->
        <a href="{{ route('logout') }}">Logout</a>
        <a href="/registrar-sheets">Registrar</a>
        <a href="/slp-sheets">SLP</a>
    </div>

    <h1>Add Entry
        <button type="button" class="red-btn" onclick="window.location.href='/slp-sheets'" style="float: right;">Back</button>
    </h1>

    <div id="addEntryForm">
        <form id="entryForm">
            <input type="text" name="slmisId" placeholder="SLMIS ID" value="">
            <input type="text" name="lastName" placeholder="Last Name" value="">
            <input type="text" name="firstName" placeholder="First Name" value="">
            <input type="text" name="middleInitial" placeholder="Middle Initial" value="">
            <input type="text" name="idNumber" placeholder="ID Number" value="">
            <input type="text" name="academicProgram" placeholder="Academic Program" value="">
            <input type="text" name="slProjectTitle" placeholder="SL Project Title" value="">
            <input type="text" name="siteOfProject" placeholder="Site of Project" value="">
            <input type="text" name="partnerInstitution" placeholder="Partner Institution" value="">
            <input type="text" name="semester" placeholder="Semester" value="">
            <input type="text" name="schoolYear" placeholder="School Year" value="">
            <input type="text" name="subjectCode" placeholder="Subject Code" value="">
            <input type="text" name="faculty" placeholder="Faculty" value="">
            <input type="text" name="projectCode" placeholder="Project Code" value="">
            <input type="text" name="verified" placeholder="Verified" value="">
            <button type="button" class="success-btn" id="submitBtn">Submit</button>
        </form>
    </div>

    <script>
        $(document).ready(function() {
            // Handle form submission
            $('#submitBtn').click(function(event) {
                // Prevent default button behavior
                event.preventDefault();

                // Check if required fields are empty
                var requiredFields = ['slmisId', 'lastName', 'firstName', 'idNumber', 'academicProgram',
                    'slProjectTitle', 'siteOfProject', 'partnerInstitution', 'semester',
                    'schoolYear', 'subjectCode', 'faculty', 'projectCode', 'verified'
                ];
                var emptyFields = requiredFields.filter(function(fieldName) {
                    return $('#entryForm input[name="' + fieldName + '"]').val().trim() === '';
                });

                if (emptyFields.length > 0) {
                    alert('Please fill in all required fields.');
                    return; // Prevent form submission
                }

                // Get form data
                var formData = $('#entryForm').serializeArray();
                var entryData = {};
                formData.forEach(function(input) {
                    entryData[input.name] = input.value;
                });

                // Retrieve access token from the session
                var accessToken = "{{ $accessToken }}";

                $.ajax({
                    url: '/submit-form',
                    type: 'POST',
                    dataType: 'json',
                    data: JSON.stringify(entryData),
                    contentType: 'application/json',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                        'Authorization': 'Bearer ' + accessToken
                    },
                    success: function(response) {
                        if (response && response.success) {
                            // Clear form fields on successful submission
                            $('#entryForm')[0].reset();
                            alert('Entry added successfully!');
                        } else {
                            console.log(response);
                            alert('Failed to add entry. Please try again.');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error(error);
                        alert('Failed to add entry. Please try again.');
                    }
                });
            });
        });
    </script>


</body>

</html>
