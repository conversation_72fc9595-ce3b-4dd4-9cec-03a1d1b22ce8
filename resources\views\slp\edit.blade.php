<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Entry</title>
    <!-- Include your CSS file -->
    <link rel="stylesheet" type="text/css" href="{{ asset('resources/css/slp.css') }}">
    <meta name="csrf-token" content="{{ csrf_token() }}">
</head>

<body>
    <!-- Navbar -->
    <div class="navbar">
        <img src="https://www.xu.edu.ph/images/system_images/logo.png" alt="Profile Picture" class="profile-pic">
        <!-- Navbar links -->
        <a href="{{ route('logout') }}">Logout</a>
        <a href="/registrar-sheets">Registrar</a>
        <a href="/slp-sheets">SLP</a>
    </div>

    <!-- Edit Form -->
    <div class="header-container">
        <h2>Edit Entry</h2>
        <button type="button" class="red-btn" id="topRightBtn">Back</button>
    </div>
    <form id="editForm">
        <!-- Input fields for editing entry data -->
        <input type="hidden" name="slmisId" value="">
        <input type="text" name="lastName" placeholder="Last Name" value="">
        <input type="text" name="firstName" placeholder="First Name" value="">
        <input type="text" name="middleInitial" placeholder="Middle Initial" value="">
        <input type="text" name="idNumber" placeholder="ID Number" value="">
        <input type="text" name="academicProgram" placeholder="Academic Program" value="">
        <input type="text" name="slProjectTitle" placeholder="SL Project Title" value="">
        <input type="text" name="siteOfProject" placeholder="Site of Project" value="">
        <input type="text" name="partnerInstitution" placeholder="Partner Institution" value="">
        <input type="text" name="semester" placeholder="Semester" value="">
        <input type="text" name="schoolYear" placeholder="School Year" value="">
        <input type="text" name="subjectCode" placeholder="Subject Code" value="">
        <input type="text" name="faculty" placeholder="Faculty" value="">
        <input type="text" name="projectCode" placeholder="Project Code" value="">
        <input type="text" name="verified" placeholder="Verified" value="">
        <button type="button" class="success-btn" id="updateBtn">Update</button>
    </form>

    <!-- Include jQuery -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <!-- Include your JavaScript file -->
    <script src="{{ asset('resources/js/edit.js') }}"></script>

    <script>
        $(document).ready(function() {
            $('#topRightBtn').click(function() {
                window.location.href = '/slp-sheets'; // Navigate to /slp-sheets
            });

            var urlParams = new URLSearchParams(window.location.search);
            var slmisId = urlParams.get('slmisId');

            if (slmisId) {
                $.ajax({
                    url: '/fetch-entry/' + slmisId,
                    type: 'GET',
                    dataType: 'json',
                    success: function(response) {
                        if (response && response.success) {
                            populateEditForm(response.data);
                        } else {
                            console.log(response);
                            alert('Failed to fetch entry data. Please try again.');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error(error);
                        alert('Failed to fetch entry data. Please try again.');
                    }
                });
            } else {
                alert('SLMIS ID not found in URL.');
            }

            // Function to populate edit form fields with data
            function populateEditForm(data) {
                $('input[name="slmisId"]').val(data.slmisId);
                $('input[name="lastName"]').val(data.lastName);
                $('input[name="firstName"]').val(data.firstName);
                $('input[name="middleInitial"]').val(data.middleInitial);
                $('input[name="idNumber"]').val(data.idNumber);
                $('input[name="academicProgram"]').val(data.academicProgram);
                $('input[name="slProjectTitle"]').val(data.slProjectTitle);
                $('input[name="siteOfProject"]').val(data.siteOfProject);
                $('input[name="partnerInstitution"]').val(data.partnerInstitution);
                $('input[name="semester"]').val(data.semester);
                $('input[name="schoolYear"]').val(data.schoolYear);
                $('input[name="subjectCode"]').val(data.subjectCode);
                $('input[name="faculty"]').val(data.faculty);
                $('input[name="projectCode"]').val(data.projectCode);
                $('input[name="verified"]').val(data.verified);
            }

            // Handle form submission
            $('#updateBtn').click(function(event) {
                // Prevent default button behavior
                event.preventDefault();

                // Get form data
                var formData = $('#editForm').serialize();
                
                // Perform AJAX request to update entry
                $.ajax({
                    url: '/update-entry/' + $('input[name="slmisId"]').val(), // Correct endpoint for updating entries
                    type: 'POST',
                    dataType: 'json',
                    data: formData,
                    contentType: 'application/x-www-form-urlencoded', // Correct content type for serialized form data
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        if (response && response.success) {
                            alert('Entry updated successfully!');
                        } else {
                            console.log(response);
                            alert('Failed to update entry. Please try again.');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error:', xhr.responseText);
                        alert('Failed to update entry. Please try again.');
                    }
                });
            });
        });
    </script>
</body>

</html>
