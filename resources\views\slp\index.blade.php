<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SLP</title>
    <!-- Include DataTables CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.css">

    <!-- Include jQuery -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

    <!-- Include DataTables JS -->
    <script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.js"></script>

    <!-- Include FontAwesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">

    <!-- Link to your separated CSS file -->
    <link rel="stylesheet" type="text/css" href="{{ asset('resources/css/slp.css') }}">
</head>

<body>

    <!-- Navbar -->
    <div class="navbar">
        <img src="https://www.xu.edu.ph/images/system_images/logo.png" alt="Profile Picture" class="profile-pic">
        <!-- Navbar links -->
        <a href="{{ route('logout') }}">Logout</a>
        <a href="/registrar-sheets">Registrar</a>
        <a href="/slp-sheets">SLP</a>
    </div>

    <!-- Filters -->
    <div class="filters-container">
        <input type="text" class="filter-input" id="filterSemester" placeholder="Semester">
        <input type="text" class="filter-input" id="filterSchoolYear" placeholder="School Year">
        <input type="text" class="filter-input" id="filterCollegesEngaged" placeholder="Program">
        <input type="text" class="filter-input" id="filterAreas" placeholder="Areas">
        <select name="displinary" id="filterDisplinary">
            <option value="">None</option>
            <option value="inter">Interdisciplinary</option>
            <option value="multi">Multidisciplinary</option>
        </select>
        <button class="secondary-btn" id="filterSearchBtn">Filter Search</button>
    </div>

    <button class="success-btn" id="addEntryBtn">Add Entry</button>

    <table id="dataTable">
        <thead>
            <tr>
                <th>SLMIS ID</th>
                <th>Last Name</th>
                <th>First Name</th>
                <th>Middle Initial</th>
                <th>ID Number</th>
                <th>Academic Program</th>
                <th>SL Project Title</th>
                <th>Site of Project</th>
                <th>Partner Institution</th>
                <th>Semester</th>
                <th>School Year</th>
                <th>Subject Code</th>
                <th>Faculty</th>
                <th>Project Code</th>
                <th>Verified</th>
                <th>Edit</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($values as $index => $row)
                <tr id="row{{ $index }}">
                    @foreach ($row as $cell)
                        <td>{{ $cell }}</td>
                    @endforeach
                    <td><button class="action-btn edit-btn" data-row="{{ json_encode($row) }}">Edit</button></td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <script>
        $(document).ready(function() {
            var table = $('#dataTable').DataTable({
                "columnDefs": [
                    {
                        "targets": 6, // Index of the "SL Project Title" column
                        "render": function(data, type, row) {
                            var truncatedText = data.length > 20 ? data.substr(0, 20) + '...' : data;
                            return '<span class="sl-project-title" data-full-text="' + data + '">' + truncatedText + '</span>';
                        }
                    },
                    {
                        "targets": 8, // Index of the "Partner Institution" column
                        "render": function(data, type, row) {
                            var truncatedText = data.length > 20 ? data.substr(0, 20) + '...' : data;
                            return '<span class="partner-institution" data-full-text="' + data + '">' + truncatedText + '</span>';
                        }
                    },
                    {
                        "targets": 12, // Index of the "Faculty" column
                        "render": function(data, type, row) {
                            var truncatedText = data.length > 20 ? data.substr(0, 20) + '...' : data;
                            return '<span class="faculty" data-full-text="' + data + '">' + truncatedText + '</span>';
                        }
                    },
                    {
                        "targets": 14, // Index of the "Verified" column
                        "render": function(data, type, row) {
                            if (data.trim().toLowerCase() === 'yes') {
                                return '<span class="verified-icon"><i class="fas fa-check-circle" style="color: green;"></i></span>';
                            } else if (data.trim().toLowerCase() === 'no') {
                                return '<span class="verified-icon"><i class="fas fa-times-circle" style="color: red;"></i></span>';
                            } else {
                                return data;
                            }
                        }
                    }
                ]
            });

            if (window.location.href.includes("/slp-sheets")) {
                $(".navbar a[href='/slp-sheets']").addClass("active");
            }

            $('#addEntryBtn').click(function() {
                window.location.href = '/add-entry';
            });

            $('#filterSearchBtn').click(function() {
                table.columns(9).search($('#filterSemester').val()).draw();
                table.columns(10).search($('#filterSchoolYear').val()).draw(); 
                table.columns(5).search($('#filterCollegesEngaged').val()).draw(); 
                table.columns(7).search($('#filterAreas').val()).draw(); 
                table.columns(11).search($('#filterDisplinary').val()).draw(); 
            });

            $('.edit-btn').click(function() {
                var rowData = $(this).closest('tr').find('td').map(function() {
                    return $(this).text();
                }).get();
                window.location.href = '/edit?slmisId=' + rowData[0];
            });

            $('#dataTable').on('click', '.sl-project-title', function() {
                var fullText = $(this).data('full-text');
                var currentText = $(this).text();
                if (currentText.endsWith('...')) {
                    $(this).text(fullText);
                } else {
                    var truncatedText = fullText.length > 20 ? fullText.substr(0, 20) + '...' : fullText;
                    $(this).text(truncatedText);
                }
            });

            $('#dataTable').on('click', '.partner-institution', function() {
                var fullText = $(this).data('full-text');
                var currentText = $(this).text();
                if (currentText.endsWith('...')) {
                    $(this).text(fullText);
                } else {
                    var truncatedText = fullText.length > 20 ? fullText.substr(0, 20) + '...' : fullText;
                    $(this).text(truncatedText);
                }
            });

            $('#dataTable').on('click', '.faculty', function() {
                var fullText = $(this).data('full-text');
                var currentText = $(this).text();
                if (currentText.endsWith('...')) {
                    $(this).text(fullText);
                } else {
                    var truncatedText = fullText.length > 20 ? fullText.substr(0, 20) + '...' : fullText;
                    $(this).text(truncatedText);
                }
            });
        });
    </script>

</body>

</html>
