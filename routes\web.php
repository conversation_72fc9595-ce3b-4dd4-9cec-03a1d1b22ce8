<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\RegistrarSheetsController;
use App\Http\Controllers\SlpSheetsController;
use App\Http\Middleware\CheckRole;
use App\Http\Controllers\AddController;
use App\Http\Controllers\SubmitFormController;
use App\Http\Controllers\EditController;

// Define the home route
Route::get('/', function () {
    return view('auth.login');
})->name('home');

Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
Route::get('/auth/google', [LoginController::class, 'redirectToGoogle']);
Route::get('/auth/google/callback', [LoginController::class, 'handleGoogleCallback']);
//Route::get('/logout', [LoginController::class, 'logout']);
Route::get('/logout', [LoginController::class, 'logout'])->name('logout');



// For users with role_as = 0
Route::middleware([CheckRole::class . ':0'])->group(function () {
    Route::get('/registrar-sheets', [RegistrarSheetsController::class, 'index'])->name('registrar-sheets'); // Add a name to the route
});

// For users with role_as = 1
Route::middleware([CheckRole::class . ':1'])->group(function () {
    Route::get('/registrar-sheets', [RegistrarSheetsController::class, 'index'])->name('registrar-sheets'); // Add a name to the route
    Route::get('/slp-sheets', [SlpSheetsController::class, 'index'])->name('slp-sheets'); // Add a name to the route
    Route::get('/add-entry', [AddController::class, 'index'])->name('add');
    Route::post('/submit-form', [SubmitFormController::class, 'submitForm']);
    Route::get('/edit', [EditController::class, 'editindex'])->name('edit');
    Route::get('/fetch-entry/{slmisId}', [SubmitFormController::class, 'fetchEntry']);
    Route::post('/update-entry/{slmisId}', [SubmitFormController::class, 'updateEntry']);

});
